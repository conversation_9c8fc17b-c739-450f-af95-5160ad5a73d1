{"name": "InkSight", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.0", "epubjs": "^0.3.93", "react": "19.1.0", "react-native": "0.80.0", "react-native-document-picker": "^9.3.1", "react-native-fs": "^2.20.0", "react-native-pdf": "^6.7.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^8.57.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "jest": "^29.6.3", "lint-staged": "^16.1.2", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}